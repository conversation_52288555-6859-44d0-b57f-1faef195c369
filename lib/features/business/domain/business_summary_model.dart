class BusinessSummaryModel {
  final int id;
  final String name;
  final String slug;
  final String? logoUrl;
  final String address;
  final double averageRating;
  final int reviewCount;
  final double? distanceKm;

  BusinessSummaryModel({
    required this.id,
    required this.name,
    required this.slug,
    this.logoUrl,
    required this.address,
    required this.averageRating,
    required this.reviewCount,
    this.distanceKm,
  });

  factory BusinessSummaryModel.fromJson(Map<String, dynamic> json) {
    return BusinessSummaryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      logoUrl: json['logo_url'] as String?,
      address: json['address'] as String,
      averageRating: (json['average_rating'] as num).toDouble(),
      reviewCount: json['review_count'] as int,
      distanceKm: (json['distance_km'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'logo_url': logoUrl,
      'address': address,
      'average_rating': averageRating,
      'review_count': reviewCount,
      'distance_km': distanceKm,
    };
  }
}
