import '../../discovery/domain/category_model.dart';
import 'business_hour_model.dart';
import 'business_media_model.dart';
import 'review_model.dart';

class BusinessDetailModel {
  final int id;
  final String name;
  final String slug;
  final String? logoUrl;
  final String address;
  final double averageRating;
  final int reviewCount;
  final double? distanceKm;
  final String description;
  final String phoneNumber;
  final String? websiteUrl;
  final List<BusinessHourModel> hours;
  final List<BusinessMediaModel> media;
  final List<CategoryModel> categories;
  final List<ReviewModel> reviews;

  BusinessDetailModel({
    required this.id,
    required this.name,
    required this.slug,
    this.logoUrl,
    required this.address,
    required this.averageRating,
    required this.reviewCount,
    this.distanceKm,
    required this.description,
    required this.phoneNumber,
    this.websiteUrl,
    required this.hours,
    required this.media,
    required this.categories,
    required this.reviews,
  });

  factory BusinessDetailModel.fromJson(Map<String, dynamic> json) {
    return BusinessDetailModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      logoUrl: json['logo_url'] as String?,
      address: json['address'] as String,
      averageRating: (json['average_rating'] as num).toDouble(),
      reviewCount: json['review_count'] as int,
      distanceKm: (json['distance_km'] as num?)?.toDouble(),
      description: json['description'] as String,
      phoneNumber: json['phone_number'] as String,
      websiteUrl: json['website_url'] as String?,
      hours: (json['hours'] as List<dynamic>)
          .map((hour) => BusinessHourModel.fromJson(hour as Map<String, dynamic>))
          .toList(),
      media: (json['media'] as List<dynamic>)
          .map((media) => BusinessMediaModel.fromJson(media as Map<String, dynamic>))
          .toList(),
      categories: (json['categories'] as List<dynamic>)
          .map((category) => CategoryModel.fromJson(category as Map<String, dynamic>))
          .toList(),
      reviews: (json['reviews'] as List<dynamic>)
          .map((review) => ReviewModel.fromJson(review as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'logo_url': logoUrl,
      'address': address,
      'average_rating': averageRating,
      'review_count': reviewCount,
      'distance_km': distanceKm,
      'description': description,
      'phone_number': phoneNumber,
      'website_url': websiteUrl,
      'hours': hours.map((hour) => hour.toJson()).toList(),
      'media': media.map((m) => m.toJson()).toList(),
      'categories': categories.map((category) => category.toJson()).toList(),
      'reviews': reviews.map((review) => review.toJson()).toList(),
    };
  }
}
