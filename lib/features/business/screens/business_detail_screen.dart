import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/business_detail_model.dart';
import '../data/business_repository.dart';
import '../../profile/providers/favorites_notifier.dart';
import '../../../shared/widgets/review_card.dart';

class BusinessDetailScreen extends StatelessWidget {
  final String slug;

  const BusinessDetailScreen({super.key, required this.slug});

  @override
  Widget build(BuildContext context) {
    return FutureProvider<BusinessDetailModel?>(
      create: (context) => _getBusinessDetails(context),
      initialData: null,
      child: Consumer<BusinessDetailModel?>(
        builder: (context, business, child) {
          if (business == null) {
            return Scaffold(
              appBar: AppBar(),
              body: const Center(child: CircularProgressIndicator()),
            );
          }

          return Scaffold(
            body: CustomScrollView(
              slivers: [
                // Media Gallery App Bar
                SliverAppBar(
                  expandedHeight: 300,
                  pinned: true,
                  flexibleSpace: FlexibleSpaceBar(
                    background: _buildMediaGallery(business),
                  ),
                  actions: [
                    // Favorite Button
                    Consumer<FavoritesNotifier>(
                      builder: (context, favoritesNotifier, child) {
                        final isFavorite = favoritesNotifier.isFavorite(
                          business.id,
                        );
                        return IconButton(
                          icon: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? Colors.red : Colors.white,
                          ),
                          onPressed: () {
                            favoritesNotifier.toggleFavorite(business.id);
                          },
                        );
                      },
                    ),
                  ],
                ),

                // Business Info
                SliverList(
                  delegate: SliverChildListDelegate([
                    _buildBusinessInfo(context, business),
                    _buildContactInfo(context, business),
                    _buildHours(context, business),
                    _buildCategories(context, business),
                    _buildReviews(context, business),
                    const SizedBox(height: 100),
                  ]),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<BusinessDetailModel?> _getBusinessDetails(BuildContext context) async {
    try {
      final businessRepository = context.read<BusinessRepository>();
      return await businessRepository.getBusinessDetails(slug);
    } catch (e) {
      return null;
    }
  }

  Widget _buildMediaGallery(BusinessDetailModel business) {
    if (business.media.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: Icon(Icons.business, size: 64, color: Colors.grey),
        ),
      );
    }

    return PageView.builder(
      itemCount: business.media.length,
      itemBuilder: (context, index) {
        final media = business.media[index];
        if (media.isImage) {
          return CachedNetworkImage(
            imageUrl: media.mediaUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey[300],
              child: const Center(child: CircularProgressIndicator()),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[300],
              child: const Center(child: Icon(Icons.error)),
            ),
          );
        }
        // For videos, show a placeholder with play button
        return Stack(
          children: [
            CachedNetworkImage(
              imageUrl: media.mediaUrl, // Assuming thumbnail URL
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[300],
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: const Center(child: Icon(Icons.error)),
              ),
            ),
            const Center(
              child: Icon(
                Icons.play_circle_filled,
                size: 64,
                color: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBusinessInfo(
    BuildContext context,
    BusinessDetailModel business,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Business Name
          Text(
            business.name,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),

          // Rating and Reviews
          Row(
            children: [
              RatingBarIndicator(
                rating: business.averageRating,
                itemBuilder: (context, index) =>
                    const Icon(Icons.star, color: Colors.amber),
                itemCount: 5,
                itemSize: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${business.averageRating.toStringAsFixed(1)} (${business.reviewCount} reviews)',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Address
          Row(
            children: [
              Icon(FeatherIcons.mapPin, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  business.address,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),

          if (business.distanceKm != null) ...[
            const SizedBox(height: 4),
            Text(
              '${business.distanceKm!.toStringAsFixed(1)} km away',
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[500]),
            ),
          ],

          const SizedBox(height: 16),

          // Description
          Text(
            business.description,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo(BuildContext context, BusinessDetailModel business) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),

          // Phone
          ListTile(
            leading: const Icon(FeatherIcons.phone),
            title: Text(business.phoneNumber),
            contentPadding: EdgeInsets.zero,
            onTap: () => _launchUrl('tel:${business.phoneNumber}'),
          ),

          // Website
          if (business.websiteUrl != null)
            ListTile(
              leading: const Icon(FeatherIcons.globe),
              title: Text(business.websiteUrl!),
              contentPadding: EdgeInsets.zero,
              onTap: () => _launchUrl(business.websiteUrl!),
            ),
        ],
      ),
    );
  }

  Widget _buildHours(BuildContext context, BusinessDetailModel business) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Hours',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          ...business.hours.map(
            (hour) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(hour.dayName, style: GoogleFonts.poppins(fontSize: 14)),
                  Text(
                    hour.displayTime,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: hour.isClosed ? Colors.red : Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategories(BuildContext context, BusinessDetailModel business) {
    if (business.categories.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: business.categories
                .map(
                  (category) => Chip(
                    label: Text(category.name),
                    backgroundColor: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    labelStyle: GoogleFonts.poppins(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildReviews(BuildContext context, BusinessDetailModel business) {
    if (business.reviews.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reviews',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          ...business.reviews.map((review) => ReviewCard(review: review)),
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }
}
