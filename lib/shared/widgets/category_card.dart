import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../features/discovery/domain/category_model.dart';
import '../../features/search/providers/search_notifier.dart';

class CategoryCard extends StatelessWidget {
  final CategoryModel category;

  const CategoryCard({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 80,
      child: Card(
        margin: EdgeInsets.zero,
        child: InkWell(
          onTap: () => _onCategoryTap(context),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Category Icon
                SizedBox(
                  width: 40,
                  height: 40,
                  child: category.iconUrl != null
                      ? CachedNetworkImage(
                          imageUrl: category.iconUrl!,
                          fit: BoxFit.contain,
                          placeholder: (context, url) => Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.category,
                              color: Theme.of(context).colorScheme.primary,
                              size: 24,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.category,
                              color: Theme.of(context).colorScheme.primary,
                              size: 24,
                            ),
                          ),
                        )
                      : Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            _getCategoryIcon(category.name),
                            color: Theme.of(context).colorScheme.primary,
                            size: 24,
                          ),
                        ),
                ),
                
                const SizedBox(height: 8),
                
                // Category Name
                Text(
                  category.name,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onCategoryTap(BuildContext context) {
    // Navigate to search screen and search by category
    context.go('/search');
    
    // Trigger search by category
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SearchNotifier>().searchByCategory(category.id);
    });
  }

  IconData _getCategoryIcon(String categoryName) {
    final name = categoryName.toLowerCase();
    
    if (name.contains('restaurant') || name.contains('food') || name.contains('dining')) {
      return Icons.restaurant;
    } else if (name.contains('shop') || name.contains('retail') || name.contains('store')) {
      return Icons.shopping_bag;
    } else if (name.contains('health') || name.contains('medical') || name.contains('doctor')) {
      return Icons.local_hospital;
    } else if (name.contains('beauty') || name.contains('salon') || name.contains('spa')) {
      return Icons.face;
    } else if (name.contains('auto') || name.contains('car') || name.contains('garage')) {
      return Icons.car_repair;
    } else if (name.contains('hotel') || name.contains('accommodation') || name.contains('lodging')) {
      return Icons.hotel;
    } else if (name.contains('gym') || name.contains('fitness') || name.contains('sport')) {
      return Icons.fitness_center;
    } else if (name.contains('education') || name.contains('school') || name.contains('learning')) {
      return Icons.school;
    } else if (name.contains('entertainment') || name.contains('fun') || name.contains('recreation')) {
      return Icons.movie;
    } else if (name.contains('service') || name.contains('professional')) {
      return Icons.work;
    } else {
      return Icons.category;
    }
  }
}
