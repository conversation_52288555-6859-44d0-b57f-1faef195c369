import 'package:dio/dio.dart';
import '../../business/domain/business_summary_model.dart';

class UserRepository {
  final Dio _dio;

  UserRepository(this._dio);

  Future<void> toggleFavorite(int businessId) async {
    try {
      final response = await _dio.post('/businesses/$businessId/favorite');

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to update favorite');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to add favorites');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Business not found');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<List<int>> getFavoriteIds() async {
    try {
      final response = await _dio.get('/me/favorites');

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data.map((item) => item['business_id'] as int).toList();
      } else {
        throw Exception('Failed to load favorites');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to view favorites');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<List<BusinessSummaryModel>> getFavoriteBusinesses({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/me/favorites/businesses', queryParameters: {
        'page': page,
        'limit': limit,
      });

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map((business) => BusinessSummaryModel.fromJson(business as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to load favorite businesses');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to view favorites');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }
}
