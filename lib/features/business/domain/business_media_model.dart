class BusinessMediaModel {
  final String mediaUrl;
  final String mediaType; // 'image' or 'video'

  BusinessMediaModel({
    required this.mediaUrl,
    required this.mediaType,
  });

  factory BusinessMediaModel.fromJson(Map<String, dynamic> json) {
    return BusinessMediaModel(
      mediaUrl: json['media_url'] as String,
      mediaType: json['media_type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'media_url': mediaUrl,
      'media_type': mediaType,
    };
  }

  bool get isImage => mediaType == 'image';
  bool get isVideo => mediaType == 'video';
}
