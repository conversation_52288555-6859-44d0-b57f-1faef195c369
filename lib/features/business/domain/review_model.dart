import '../../auth/domain/user_model.dart';

class ReviewModel {
  final int id;
  final int rating;
  final String comment;
  final String? ownerReply;
  final String createdAt;
  final SimpleUserModel user;

  ReviewModel({
    required this.id,
    required this.rating,
    required this.comment,
    this.ownerReply,
    required this.createdAt,
    required this.user,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] as int,
      rating: json['rating'] as int,
      comment: json['comment'] as String,
      ownerReply: json['owner_reply'] as String?,
      createdAt: json['created_at'] as String,
      user: SimpleUserModel.fromJson(json['user'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rating': rating,
      'comment': comment,
      'owner_reply': ownerReply,
      'created_at': createdAt,
      'user': user.toJson(),
    };
  }
}
