class CategoryModel {
  final int id;
  final String name;
  final String slug;
  final String? iconUrl;
  final List<CategoryModel> children;

  CategoryModel({
    required this.id,
    required this.name,
    required this.slug,
    this.iconUrl,
    this.children = const [],
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      iconUrl: json['icon_url'] as String?,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => CategoryModel.fromJson(child as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'icon_url': iconUrl,
      'children': children.map((child) => child.toJson()).toList(),
    };
  }
}
