import 'package:flutter/foundation.dart';
import '../data/user_repository.dart';
import '../../business/domain/business_summary_model.dart';

class FavoritesNotifier extends ChangeNotifier {
  final UserRepository _userRepository;
  
  List<int> _favoriteBusinessIds = [];
  bool _isLoading = false;
  String? _errorMessage;

  FavoritesNotifier(this._userRepository);

  // Getters
  List<int> get favoriteBusinessIds => _favoriteBusinessIds;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Check if a business is favorited
  bool isFavorite(int businessId) {
    return _favoriteBusinessIds.contains(businessId);
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Fetch favorites from server
  Future<void> fetchFavorites() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final favoriteIds = await _userRepository.getFavoriteIds();
      _favoriteBusinessIds = favoriteIds;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Toggle favorite with optimistic UI
  Future<void> toggleFavorite(int businessId) async {
    // Optimistic UI update
    final wasInFavorites = _favoriteBusinessIds.contains(businessId);
    
    if (wasInFavorites) {
      _favoriteBusinessIds.remove(businessId);
    } else {
      _favoriteBusinessIds.add(businessId);
    }
    
    // Notify listeners immediately for instant UI feedback
    notifyListeners();

    try {
      // Make API call in background
      await _userRepository.toggleFavorite(businessId);
    } catch (e) {
      // Revert the optimistic update on failure
      if (wasInFavorites) {
        _favoriteBusinessIds.add(businessId);
      } else {
        _favoriteBusinessIds.remove(businessId);
      }
      
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      notifyListeners();
    }
  }

  // Get favorite businesses
  Future<List<BusinessSummaryModel>> getFavoriteBusinesses({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      return await _userRepository.getFavoriteBusinesses(
        page: page,
        limit: limit,
      );
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      notifyListeners();
      return [];
    }
  }

  // Clear all favorites (for logout)
  void clearFavorites() {
    _favoriteBusinessIds.clear();
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }
}
